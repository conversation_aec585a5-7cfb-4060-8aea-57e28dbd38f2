import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate, Link } from 'react-router-dom';
import { useNotification } from '../contexts/NotificationContext';
import { logger } from '../utils/logger';
import api from '../services/api';
import '../styles/auth.css';

const ResetPassword = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { showSuccessMessage, showErrorMessage } = useNotification();

  const [formData, setFormData] = useState({
    newPassword: '',
    confirmPassword: ''
  });
  const [loading, setLoading] = useState(false);
  const [verifying, setVerifying] = useState(true);
  const [tokenValid, setTokenValid] = useState(false);
  const [userEmail, setUserEmail] = useState('');
  const [passwordStrength, setPasswordStrength] = useState({
    score: 0,
    isValid: false,
    text: 'Very Weak',
    color: '#ef4444'
  });

  const token = searchParams.get('token');
  const email = searchParams.get('email');

  // Verify token on component mount
  useEffect(() => {
    const verifyToken = async () => {
      if (!token) {
        showErrorMessage('INVALID_TOKEN', 'Invalid reset link. Please request a new password reset.');
        navigate('/forgot-password');
        return;
      }

      try {
        const response = await api.post('/api/auth/verify-reset-token', { token });
        
        if (response.data.success) {
          setTokenValid(true);
          setUserEmail(response.data.data.email);
          logger.info('Reset token verified successfully');
        } else {
          showErrorMessage('INVALID_TOKEN', 'Invalid or expired reset link. Please request a new password reset.');
          navigate('/forgot-password');
        }
      } catch (error) {
        logger.error('Token verification failed:', error);
        showErrorMessage('INVALID_TOKEN', 'Invalid or expired reset link. Please request a new password reset.');
        navigate('/forgot-password');
      } finally {
        setVerifying(false);
      }
    };

    verifyToken();
  }, [token, navigate, showErrorMessage]);

  // Password strength checker
  const checkPasswordStrength = (password) => {
    let score = 0;
    const checks = {
      length: password.length >= 8,
      lowercase: /[a-z]/.test(password),
      uppercase: /[A-Z]/.test(password),
      number: /[0-9]/.test(password),
      special: /[@$!%*?&]/.test(password)
    };

    score = Object.values(checks).filter(Boolean).length;

    const strengthLevels = [
      { text: 'Very Weak', color: '#ef4444' },
      { text: 'Weak', color: '#f97316' },
      { text: 'Fair', color: '#eab308' },
      { text: 'Good', color: '#84cc16' },
      { text: 'Strong', color: '#22c55e' }
    ];

    return {
      score,
      isValid: score >= 4,
      text: strengthLevels[score] ? strengthLevels[score].text : 'Very Weak',
      color: strengthLevels[score] ? strengthLevels[score].color : '#ef4444',
      checks
    };
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Check password strength for new password
    if (name === 'newPassword') {
      const strength = checkPasswordStrength(value);
      setPasswordStrength(strength);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const { newPassword, confirmPassword } = formData;

    // Validation
    if (!newPassword || !confirmPassword) {
      showErrorMessage('VALIDATION_ERROR', 'Please fill in all fields.');
      return;
    }

    if (newPassword !== confirmPassword) {
      showErrorMessage('VALIDATION_ERROR', 'Passwords do not match.');
      return;
    }

    if (!passwordStrength.isValid) {
      showErrorMessage('VALIDATION_ERROR', 'Password does not meet security requirements.');
      return;
    }

    setLoading(true);

    try {
      const response = await api.post('/api/auth/reset-password-with-token', {
        token,
        newPassword,
        confirmPassword
      });

      if (response.data.success) {
        showSuccessMessage('PASSWORD_RESET_SUCCESS', 'Password reset successfully! You can now log in with your new password.');
        logger.info('Password reset completed successfully');
        
        // Redirect to login after a short delay
        setTimeout(() => {
          navigate('/login', { 
            state: { 
              message: 'Password reset successful. Please log in with your new password.',
              email: userEmail 
            }
          });
        }, 2000);
      } else {
        showErrorMessage('PASSWORD_RESET_FAILED', response.data.message || 'Failed to reset password.');
      }
    } catch (error) {
      logger.error('Password reset failed:', error);
      const errorMessage = error.response?.data?.message || 'Failed to reset password. Please try again.';
      showErrorMessage('PASSWORD_RESET_FAILED', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  if (verifying) {
    return (
      <div className="auth-container">
        <div className="auth-card">
          <div className="auth-header">
            <div className="loading-spinner">
              <i className="fas fa-spinner fa-spin"></i>
            </div>
            <h1>Verifying Reset Link</h1>
            <p>Please wait while we verify your password reset link...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!tokenValid) {
    return null; // Component will redirect
  }

  return (
    <div className="auth-container">
      <div className="auth-card">
        <div className="auth-header">
          <h1>Create New Password</h1>
          <p>Enter your new password for <strong>{userEmail}</strong></p>
        </div>

        <form onSubmit={handleSubmit} className="auth-form">
          <div className="form-group">
            <label htmlFor="newPassword">New Password</label>
            <div className="input-wrapper">
              <i className="fas fa-lock input-icon"></i>
              <input
                type="password"
                id="newPassword"
                name="newPassword"
                value={formData.newPassword}
                onChange={handleInputChange}
                placeholder="Enter your new password"
                required
                disabled={loading}
                autoComplete="new-password"
              />
            </div>
            
            {formData.newPassword && (
              <div className="password-strength">
                <div className="strength-bar">
                  <div 
                    className="strength-fill" 
                    style={{ 
                      width: `${(passwordStrength.score / 5) * 100}%`,
                      backgroundColor: passwordStrength.color 
                    }}
                  ></div>
                </div>
                <span 
                  className="strength-text" 
                  style={{ color: passwordStrength.color }}
                >
                  {passwordStrength.text}
                </span>
              </div>
            )}

            <div className="password-requirements">
              <p>Password must contain:</p>
              <ul>
                <li className={passwordStrength.checks?.length ? 'valid' : ''}>
                  <i className={`fas ${passwordStrength.checks?.length ? 'fa-check' : 'fa-times'}`}></i>
                  At least 8 characters
                </li>
                <li className={passwordStrength.checks?.lowercase ? 'valid' : ''}>
                  <i className={`fas ${passwordStrength.checks?.lowercase ? 'fa-check' : 'fa-times'}`}></i>
                  One lowercase letter
                </li>
                <li className={passwordStrength.checks?.uppercase ? 'valid' : ''}>
                  <i className={`fas ${passwordStrength.checks?.uppercase ? 'fa-check' : 'fa-times'}`}></i>
                  One uppercase letter
                </li>
                <li className={passwordStrength.checks?.number ? 'valid' : ''}>
                  <i className={`fas ${passwordStrength.checks?.number ? 'fa-check' : 'fa-times'}`}></i>
                  One number
                </li>
                <li className={passwordStrength.checks?.special ? 'valid' : ''}>
                  <i className={`fas ${passwordStrength.checks?.special ? 'fa-check' : 'fa-times'}`}></i>
                  One special character (@$!%*?&)
                </li>
              </ul>
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="confirmPassword">Confirm New Password</label>
            <div className="input-wrapper">
              <i className="fas fa-lock input-icon"></i>
              <input
                type="password"
                id="confirmPassword"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                placeholder="Confirm your new password"
                required
                disabled={loading}
                autoComplete="new-password"
              />
            </div>
            
            {formData.confirmPassword && formData.newPassword !== formData.confirmPassword && (
              <div className="password-mismatch">
                <i className="fas fa-exclamation-triangle"></i>
                Passwords do not match
              </div>
            )}
          </div>

          <button 
            type="submit" 
            className="auth-button primary"
            disabled={loading || !passwordStrength.isValid || formData.newPassword !== formData.confirmPassword}
          >
            {loading ? (
              <>
                <i className="fas fa-spinner fa-spin"></i>
                Resetting Password...
              </>
            ) : (
              <>
                <i className="fas fa-key"></i>
                Reset Password
              </>
            )}
          </button>
        </form>

        <div className="auth-footer">
          <p>
            Remember your password? 
            <Link to="/login" className="auth-link">Back to Login</Link>
          </p>
        </div>

        <div className="security-info">
          <div className="security-item">
            <i className="fas fa-shield-alt"></i>
            <span>Your password is encrypted and secure</span>
          </div>
          <div className="security-item">
            <i className="fas fa-clock"></i>
            <span>This reset link expires in 1 hour</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResetPassword;
