/* Admin Management Pages Styles - Black & White Theme */
.admin-management-container {
  padding: 32px;
  width: 100%;
  max-width: 100%;
  margin: 0;
  min-height: calc(100vh - 80px);
  background: #ffffff;
  position: relative;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* Header Styles */
.management-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding: 32px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  position: relative;
  z-index: 1;
}

.header-content h1 {
  margin: 0 0 12px 0;
  font-size: 32px;
  font-weight: 700;
  color: #000000;
  line-height: 1.2;
}

.header-content p {
  margin: 0;
  color: #333333;
  font-size: 16px;
  font-weight: 500;
}

.back-button {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 12px 20px;
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  color: #333333;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.back-button:hover {
  background: #f5f5f5;
  color: #000000;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #000000;
}

.header-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  text-align: center;
  padding: 20px 24px;
  background: #ffffff;
  border-radius: 8px;
  min-width: 120px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #000000;
}

.stat-value {
  display: block;
  font-size: 28px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 4px;
}

.stat-label {
  display: block;
  font-size: 13px;
  color: #333333;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Filters Styles */
.management-filters {
  display: flex;
  gap: 24px;
  margin-bottom: 32px;
  padding: 28px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  flex-wrap: wrap;
  position: relative;
  z-index: 1;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 160px;
}

.filter-group.search-group {
  flex: 1;
  min-width: 280px;
}

.filter-group label {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filter-group select,
.filter-group input {
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  background: #ffffff;
  color: #000000;
  font-weight: 500;
  transition: all 0.3s ease;
}

.filter-group select:focus,
.filter-group input:focus {
  outline: none;
  border-color: #000000;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* Table Styles */
.management-table-container {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  overflow-x: auto;
  overflow-y: visible;
  margin-bottom: 32px;
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 100%;
  -webkit-overflow-scrolling: touch;
}

.management-table {
  width: 100%;
  min-width: 1300px;
  border-collapse: collapse;
  table-layout: fixed;
}

.management-table th {
  background: #f5f5f5;
  padding: 20px 20px; /* Increased horizontal padding to match td */
  text-align: left;
  font-weight: 700;
  color: #000000;
  border-bottom: 2px solid #e0e0e0;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  white-space: nowrap;
}

/* Center align specific header columns */
.management-table th:nth-child(2), /* Type column */
.management-table th:nth-child(3), /* Status column */
.management-table th:nth-child(4), /* Verification column */
.management-table th:nth-child(5) { /* Joined column */
  text-align: center;
  padding: 20px 24px; /* Extra padding for badge columns */
}

/* Define column widths for better control - Users Table */
.management-table th:nth-child(1) { width: 20%; } /* User */
.management-table th:nth-child(2) { width: 10%; } /* Type */
.management-table th:nth-child(3) { width: 10%; } /* Status */
.management-table th:nth-child(4) { width: 10%; } /* Verification */
.management-table th:nth-child(5) { width: 10%; } /* Joined */
.management-table th:nth-child(6) { width: 40%; } /* Actions - Further increased to prevent dropdown cutoff */

/* Brokers Table - 7 columns (needs more space for 3 buttons + dropdown) */
.management-table th:nth-child(7) { width: 30%; min-width: 240px; } /* Actions for 7-column tables */

/* Contracts Table - 8 columns */
.management-table th:nth-child(8) { width: 20%; min-width: 160px; } /* Actions for 8-column tables */

/* Remove special broker handling - use standard styling */

.management-table td {
  padding: 20px 20px; /* Increased horizontal padding for better spacing */
  border-bottom: 1px solid #f5f5f5;
  vertical-align: middle;
  transition: all 0.3s ease;
  overflow: visible; /* Changed from hidden to visible for proper badge display */
  text-overflow: ellipsis;
  text-align: left;
}

/* Add extra padding to the Actions column to prevent dropdown cutoff */
.management-table td:nth-child(6) {
  padding-right: 50px;
  padding-left: 30px;
}

/* Ensure specific columns have proper alignment */
.management-table td:nth-child(2), /* Type column */
.management-table td:nth-child(3), /* Status column */
.management-table td:nth-child(4), /* Verification column */
.management-table td:nth-child(5) { /* Joined column */
  text-align: center;
  padding: 20px 24px; /* Extra padding for badge columns */
}

.management-table td:nth-child(6), /* Actions column */
.management-table td:nth-child(7), /* Actions column for 7-column tables */
.management-table td:nth-child(8) { /* Actions column for 8-column tables */
  text-align: left;
  padding: 20px 24px; /* Extra padding for actions column */
}

.management-table tr:hover {
  background: #f5f5f5;
}

.management-table tr:nth-child(even) {
  background: #fafafa;
}

.management-table tr:nth-child(even):hover {
  background: #f0f0f0;
}

/* User Info Styles */
.user-info {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
  min-width: 180px;
}

.user-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 18px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
  margin-right: 12px; /* Add spacing from adjacent content */
  flex-shrink: 0;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-name {
  font-weight: 600;
  color: #000000;
  font-size: 15px;
  line-height: 1.2;
}

.user-email {
  font-size: 13px;
  color: #333333;
  font-weight: 500;
}

/* Badge Styles */
.status-badge,
.type-badge {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  white-space: nowrap;
  text-align: center;
  min-width: 70px;
  max-width: 120px;
  margin: 2px 4px; /* Add margin around badges for spacing */
}

.status-badge.active {
  background: #000000;
  color: #ffffff;
  border-color: #000000;
}

.status-badge.inactive {
  background: #ffffff;
  color: #000000;
  border-color: #000000;
}

.status-badge.pending {
  background: #666666;
  color: #ffffff;
  border-color: #666666;
}

.status-badge.suspended {
  background: #333333;
  color: #ffffff;
  border-color: #333333;
}

.type-badge.individual,
.type-badge.professional,
.type-badge.broker,
.type-badge.supplier,
.type-badge.admin {
  background: #f5f5f5;
  color: #000000;
  border-color: #e0e0e0;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: nowrap;
}

.status-select {
  padding: 6px 10px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  background: #ffffff;
  color: #000000;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
  height: 36px;
  margin-left: 10px;
  box-sizing: border-box;
}

.status-select:focus {
  outline: none;
  border-color: #000000;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

.action-btn {
  padding: 0;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: #ffffff;
  color: #333333;
  flex-shrink: 0;
  box-sizing: border-box;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: #f5f5f5;
  border-color: #000000;
  color: #000000;
}

.action-btn.view,
.action-btn.download,
.action-btn.edit,
.action-btn.assign {
  background: #000000;
  color: #ffffff;
  border-color: #000000;
}

.action-btn.view:hover,
.action-btn.download:hover,
.action-btn.edit:hover,
.action-btn.assign:hover {
  background: #333333;
}

/* Ensure icons are visible in action buttons - FontAwesome 6 */
.action-btn i {
  color: inherit !important;
  font-style: normal !important;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  font-family: "Font Awesome 6 Free" !important;
  font-weight: 900 !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: auto;
  width: 36px;
  height: 36px;
  font-size: 14px;
}

.action-btn.view i,
.action-btn.download i,
.action-btn.edit i,
.action-btn.assign i {
  color: #ffffff !important;
}

.action-btn.delete i {
  color: #000000 !important;
}

.action-btn.delete:hover i {
  color: #ffffff !important;
}

/* FontAwesome 6 icon fixes with proper content codes */
.fa-eye::before { content: "\f06e"; }
.fa-key::before { content: "\f084"; }
.fa-edit::before { content: "\f044"; }
.fa-pen-to-square::before { content: "\f044"; }
.fa-trash::before { content: "\f2ed"; }
.fa-trash-can::before { content: "\f2ed"; }
.fa-download::before { content: "\f019"; }
.fa-user-plus::before { content: "\f234"; }

/* Additional icon visibility fixes */
.action-btn i.fas,
.action-btn i.fa-solid {
  font-family: "Font Awesome 6 Free" !important;
  font-weight: 900 !important;
}

/* Ensure proper spacing between table elements */
.management-table td > * {
  margin: 0 2px; /* Add small margin to all direct children of td */
}

.management-table td .status-badge,
.management-table td .type-badge {
  margin: 2px 8px;
}

/* Specific spacing for badge combinations */
.management-table td .type-badge + .status-badge {
  margin-left: 12px; /* Extra space between type and status badges */
}

/* Fix any remaining alignment issues */
.management-table tbody tr {
  border-bottom: 1px solid #f5f5f5;
}

.management-table tbody tr:hover {
  background-color: #fafafa;
}

/* Ensure action buttons container has proper spacing */
.action-buttons > * {
  margin: 0 2px; /* Add margin between action elements */
}

.action-buttons .status-select {
  margin-left: 12px !important; /* Ensure dropdown has proper separation */
}

.action-btn.delete {
  background: #ffffff;
  color: #000000;
  border-color: #000000;
}

.action-btn.delete:hover {
  background: #000000;
  color: #ffffff;
}

/* Additional Styles */
.contract-number {
  font-weight: 500;
  color: #007bff;
}

.supplier-name {
  font-weight: 500;
  color: #495057;
}

.amount {
  font-weight: 600;
  color: #28a745;
}

.license-number {
  font-family: monospace;
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.company-name {
  font-weight: 500;
  color: #495057;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #495057;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* Pagination Styles */
.management-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24px;
  padding: 28px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  position: relative;
  z-index: 1;
}

.pagination-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 20px;
  background: #000000;
  color: #ffffff;
  border: 1px solid #000000;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pagination-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: #333333;
}

.pagination-btn:disabled {
  background: #f5f5f5;
  color: #666666;
  border-color: #e0e0e0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.pagination-info {
  font-size: 14px;
  color: #000000;
  font-weight: 600;
  padding: 12px 20px;
  background: #f5f5f5;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px);
  }
}

.modal-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.2),
    0 1px 0px rgba(255, 255, 255, 0.5) inset;
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    transform: translateY(20px) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

.modal-content.large {
  max-width: 900px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28px 32px;
  border-bottom: 2px solid rgba(226, 232, 240, 0.6);
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 24px 24px 0 0;
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modal-close {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.6);
  font-size: 18px;
  color: #64748b;
  cursor: pointer;
  padding: 8px;
  border-radius: 12px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 1);
  color: #374151;
  transform: scale(1.1);
}

.modal-body {
  padding: 32px;
}

.modal-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}

.btn-cancel,
.btn-confirm {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-cancel {
  background: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
}

.btn-cancel:hover {
  background: #e9ecef;
}

.btn-confirm {
  background: #dc3545;
  color: white;
}

.btn-confirm:hover {
  background: #c82333;
}

/* User Details Styles */
.user-details-content {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.details-section {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
}

.details-section h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.btn-view-all {
  background: #007bff;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-view-all:hover {
  background: #0056b3;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item label {
  font-size: 12px;
  font-weight: 500;
  color: #6c757d;
  text-transform: uppercase;
}

.detail-item span {
  font-size: 14px;
  color: #495057;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
}

.stat-card.mini {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  border: 1px solid #dee2e6;
}

.stat-card.mini .stat-value {
  display: block;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.stat-card.mini .stat-label {
  font-size: 12px;
  color: #6c757d;
}

.activities-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  padding: 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: #f8f9fa;
}

.activity-item.high {
  border-left: 4px solid #ffc107;
}

.activity-item.critical {
  border-left: 4px solid #dc3545;
}

.activity-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.activity-type {
  font-size: 12px;
  font-weight: 500;
  color: #007bff;
  text-transform: uppercase;
}

.activity-content p {
  margin: 0;
  font-size: 14px;
  color: #495057;
}

.activity-time {
  font-size: 12px;
  color: #6c757d;
}

.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

/* Quote Management Specific Styles */
.energy-type {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #495057;
}

.energy-type i {
  color: #007bff;
}

.priority-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

.priority-badge.low {
  background: #d1ecf1;
  color: #0c5460;
}

.priority-badge.normal {
  background: #d4edda;
  color: #155724;
}

.priority-badge.high {
  background: #fff3cd;
  color: #856404;
}

.priority-badge.urgent {
  background: #f8d7da;
  color: #721c24;
}

.quotes-count {
  font-weight: 500;
  color: #007bff;
}

.status-badge.processing {
  background: #cce5ff;
  color: #004085;
}

.status-badge.sent {
  background: #d1ecf1;
  color: #0c5460;
}

/* Document Templates Styles */
.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.btn-primary {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: #0056b3;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.template-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.template-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.template-card.inactive {
  opacity: 0.6;
  border-color: #dee2e6;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.template-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #007bff;
  font-size: 20px;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #007bff;
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

.template-content h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.template-content p {
  margin: 0 0 15px 0;
  color: #6c757d;
  font-size: 14px;
  line-height: 1.4;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.type-badge.comparison {
  background: #cce5ff;
  color: #004085;
}

.type-badge.mandate {
  background: #d4edda;
  color: #155724;
}

.type-badge.authorization {
  background: #fff3cd;
  color: #856404;
}

.type-badge.contract {
  background: #f8d7da;
  color: #721c24;
}

.usage-count {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.template-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #dee2e6;
}

.last-modified {
  font-size: 12px;
  color: #6c757d;
}

.template-actions {
  display: flex;
  gap: 8px;
}

/* Settings Section */
.settings-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 25px;
  margin-bottom: 20px;
}

.settings-section h2 {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.settings-grid {
  display: grid;
  gap: 25px;
  margin-bottom: 25px;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting-item label {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.file-upload-area {
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  background: #f8f9fa;
  transition: all 0.2s ease;
  cursor: pointer;
}

.file-upload-area:hover {
  border-color: #007bff;
  background: #f0f8ff;
}

.file-upload-area i {
  font-size: 24px;
  color: #6c757d;
  margin-bottom: 8px;
  display: block;
}

.file-upload-area span {
  display: block;
  color: #6c757d;
  margin-bottom: 10px;
}

.btn-upload {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

.setting-item textarea {
  padding: 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 14px;
  resize: vertical;
  font-family: inherit;
}

.formula-settings {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.formula-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.formula-item label {
  font-size: 12px;
  font-weight: 500;
  color: #6c757d;
}

.formula-item input,
.formula-item select {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
}

.settings-actions {
  display: flex;
  justify-content: flex-end;
}

.btn-save {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-save:hover {
  background: #1e7e34;
}

/* Upload Form */
.upload-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 10px 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 14px;
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Notification Management Styles */
.notification-settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.setting-card {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.setting-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.setting-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.setting-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.setting-row label {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
}

.setting-row input,
.setting-row select {
  padding: 6px 10px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
  width: 120px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: white;
  border-bottom: 1px solid #dee2e6;
}

.table-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.notification-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 16px;
}

.notification-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.notification-title {
  font-weight: 500;
  color: #2c3e50;
  font-size: 14px;
}

.notification-message {
  font-size: 12px;
  color: #7f8c8d;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.type-badge.notification {
  background: #d1ecf1;
  color: #0c5460;
}

.recipients-count {
  font-weight: 500;
  color: #007bff;
}

.delivery-stats {
  display: flex;
  gap: 10px;
  align-items: center;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.stat-item.success {
  color: #28a745;
}

.stat-item.failed {
  color: #dc3545;
}

/* Responsive Design */

/* Desktop with sidebar considerations */
@media (min-width: 1200px) {
  .management-table {
    min-width: 1400px;
  }

  /* For tables with more columns (contracts = 8 columns) */
  .management-table th:nth-child(8) ~ th,
  .management-table td:nth-child(8) ~ td {
    min-width: 100px; /* Ensure 8+ column tables have adequate space */
  }
}

@media (min-width: 992px) and (max-width: 1199px) {
  .management-table {
    min-width: 1300px;
  }

  .management-table th,
  .management-table td {
    padding: 16px 12px; /* Reduce padding for medium screens */
  }

  /* For tables with 8+ columns, increase minimum width */
  .management-table th:nth-child(8),
  .management-table td:nth-child(8) {
    min-width: 90px;
  }

  /* Adjust action buttons for medium screens */
  .action-buttons {
    min-width: 180px;
  }

  .action-btn {
    min-width: 32px;
    min-height: 32px;
    padding: 6px 8px;
  }

  .status-select {
    min-width: 70px;
    max-width: 90px;
  }

  /* Special handling for broker tables on medium screens */
  .management-table tbody tr td:nth-child(7) .action-buttons {
    min-width: 200px;
    gap: 3px;
  }

  .management-table tbody tr td:nth-child(7) .action-btn {
    min-width: 28px;
    min-height: 28px;
    padding: 4px 6px;
    font-size: 11px;
  }
}

/* Tablet and small desktop */
@media (min-width: 769px) and (max-width: 991px) {
  .admin-management-container {
    padding: 20px;
  }

  .management-table {
    min-width: 900px; /* Increased for better action column spacing */
  }

  .management-table th,
  .management-table td {
    padding: 16px 10px;
  }

  .action-buttons {
    gap: 4px;
    min-width: 160px;
  }

  .action-btn {
    min-width: 30px;
    min-height: 30px;
    padding: 6px 8px;
    font-size: 12px;
  }

  .status-select {
    min-width: 65px;
    max-width: 80px;
    font-size: 10px;
    padding: 4px 6px;
  }

  /* Special handling for broker tables on tablet */
  .management-table tbody tr td:nth-child(7) .action-buttons {
    min-width: 180px;
    gap: 2px;
    flex-wrap: wrap;
  }

  .management-table tbody tr td:nth-child(7) .action-btn {
    min-width: 26px;
    min-height: 26px;
    padding: 3px 5px;
    font-size: 10px;
  }

  .management-table tbody tr td:nth-child(7) .status-select {
    min-width: 55px;
    max-width: 70px;
    font-size: 9px;
  }
}

/* Mobile */
@media (max-width: 768px) {
  .admin-management-container {
    padding: 15px;
  }

  .management-header {
    flex-direction: column;
    gap: 15px;
  }

  .management-filters {
    flex-direction: column;
    gap: 15px;
  }

  .filter-group {
    min-width: auto;
  }

  .management-table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: 8px;
  }

  .management-table {
    min-width: 850px; /* Increased minimum width for mobile to accommodate actions */
  }

  .management-table th,
  .management-table td {
    padding: 12px 6px;
    font-size: 13px;
  }

  .action-buttons {
    gap: 3px;
    min-width: 140px;
    flex-wrap: wrap;
  }

  .action-btn {
    min-width: 28px;
    min-height: 28px;
    padding: 4px 6px;
    font-size: 11px;
  }

  .status-select {
    min-width: 60px;
    max-width: 75px;
    font-size: 10px;
    padding: 3px 5px;
  }

  /* Special handling for broker tables on mobile */
  .management-table tbody tr td:nth-child(7) .action-buttons {
    min-width: 160px;
    gap: 2px;
    flex-wrap: wrap;
  }

  .management-table tbody tr td:nth-child(7) .action-btn {
    min-width: 24px;
    min-height: 24px;
    padding: 2px 4px;
    font-size: 9px;
  }

  .management-table tbody tr td:nth-child(7) .status-select {
    min-width: 50px;
    max-width: 65px;
    font-size: 8px;
    padding: 2px 4px;
  }

  .pagination-btn {
    padding: 8px 12px;
    font-size: 12px;
  }

  .modal-content {
    margin: 10px;
    max-width: none;
  }

  .modal-content.large {
    max-width: none;
  }

  .details-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }
}

/* Small mobile */
@media (max-width: 480px) {
  .admin-management-container {
    padding: 10px;
  }

  .management-table {
    min-width: 600px;
  }

  .management-table th,
  .management-table td {
    padding: 10px 6px;
    font-size: 12px;
  }

  .user-info {
    gap: 8px;
  }

  .user-icon {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }

  .action-buttons {
    gap: 2px;
    min-width: 100px;
  }

  .action-btn {
    min-width: 28px;
    min-height: 28px;
    padding: 4px 6px;
    font-size: 11px;
  }
}
