const AWS = require('aws-sdk');
const logger = require('../utils/logger');

// Configure AWS SES
const ses = new AWS.SES({
  region: process.env.AWS_REGION || 'eu-west-1',
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
});

class EmailService {
  constructor() {
    this.fromEmail = process.env.FROM_EMAIL || '<EMAIL>';
    this.companyName = process.env.COMPANY_NAME || 'Energy Platform';
    this.frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
  }

  async sendInvitationEmail(invitation) {
    try {
      const { userType, email, inviteeDetails, invitationToken } = invitation;
      
      const registrationUrl = `${this.frontendUrl}/register/invited?token=${invitationToken}&type=${userType.toLowerCase()}`;
      
      const emailTemplate = this.getInvitationEmailTemplate(userType, inviteeDetails, registrationUrl);
      
      const params = {
        Source: this.fromEmail,
        Destination: {
          ToAddresses: [email]
        },
        Message: {
          Subject: {
            Data: emailTemplate.subject,
            Charset: 'UTF-8'
          },
          Body: {
            Html: {
              Data: emailTemplate.htmlBody,
              Charset: 'UTF-8'
            },
            Text: {
              Data: emailTemplate.textBody,
              Charset: 'UTF-8'
            }
          }
        },
        Tags: [
          {
            Name: 'EmailType',
            Value: 'Invitation'
          },
          {
            Name: 'UserType',
            Value: userType
          }
        ]
      };

      const result = await ses.sendEmail(params).promise();
      
      logger.info(`Invitation email sent successfully to ${email}`, {
        messageId: result.MessageId,
        userType,
        invitationToken
      });

      return {
        success: true,
        messageId: result.MessageId
      };

    } catch (error) {
      logger.error('Failed to send invitation email:', error);
      throw new Error(`Failed to send invitation email: ${error.message}`);
    }
  }

  async sendReminderEmail(invitation) {
    try {
      const { userType, email, inviteeDetails, invitationToken, remindersSent } = invitation;
      
      const registrationUrl = `${this.frontendUrl}/register/invited?token=${invitationToken}&type=${userType.toLowerCase()}`;
      
      const emailTemplate = this.getReminderEmailTemplate(userType, inviteeDetails, registrationUrl, remindersSent);
      
      const params = {
        Source: this.fromEmail,
        Destination: {
          ToAddresses: [email]
        },
        Message: {
          Subject: {
            Data: emailTemplate.subject,
            Charset: 'UTF-8'
          },
          Body: {
            Html: {
              Data: emailTemplate.htmlBody,
              Charset: 'UTF-8'
            },
            Text: {
              Data: emailTemplate.textBody,
              Charset: 'UTF-8'
            }
          }
        },
        Tags: [
          {
            Name: 'EmailType',
            Value: 'Reminder'
          },
          {
            Name: 'UserType',
            Value: userType
          }
        ]
      };

      const result = await ses.sendEmail(params).promise();
      
      logger.info(`Reminder email sent successfully to ${email}`, {
        messageId: result.MessageId,
        userType,
        reminderNumber: remindersSent + 1
      });

      return {
        success: true,
        messageId: result.MessageId
      };

    } catch (error) {
      logger.error('Failed to send reminder email:', error);
      throw new Error(`Failed to send reminder email: ${error.message}`);
    }
  }

  getInvitationEmailTemplate(userType, inviteeDetails, registrationUrl) {
    const name = inviteeDetails.firstName ? 
      `${inviteeDetails.firstName} ${inviteeDetails.lastName || ''}`.trim() : 
      'there';
    
    const userTypeDisplay = userType === 'Broker' ? 'Energy Broker' : 'Energy Supplier';
    
    const subject = `Invitation to Join ${this.companyName} as ${userTypeDisplay}`;
    
    const htmlBody = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${subject}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 10px; overflow: hidden; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 40px 30px; }
        .greeting { font-size: 18px; margin-bottom: 20px; color: #2c3e50; }
        .message { margin-bottom: 30px; font-size: 16px; line-height: 1.8; }
        .cta-button { display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 50px; font-weight: 600; font-size: 16px; margin: 20px 0; transition: transform 0.2s; }
        .cta-button:hover { transform: translateY(-2px); }
        .details { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .details h3 { margin-top: 0; color: #495057; }
        .footer { background-color: #f8f9fa; padding: 20px 30px; text-align: center; font-size: 14px; color: #6c757d; }
        .expiry { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .expiry strong { color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${this.companyName}</h1>
            <p>Energy Management Platform</p>
        </div>
        
        <div class="content">
            <div class="greeting">Hello ${name},</div>
            
            <div class="message">
                <p>You have been invited to join <strong>${this.companyName}</strong> as a <strong>${userTypeDisplay}</strong>!</p>
                
                <p>Our platform connects energy professionals with clients seeking the best energy solutions. As a ${userTypeDisplay.toLowerCase()}, you'll have access to:</p>
                
                ${userType === 'Broker' ? `
                <ul>
                    <li>🎯 <strong>Client Management</strong> - Manage your assigned clients and their energy needs</li>
                    <li>📊 <strong>Quote Comparison Tools</strong> - Generate and send competitive energy quotes</li>
                    <li>💰 <strong>Commission Tracking</strong> - Monitor your earnings and commission payments</li>
                    <li>📈 <strong>Performance Analytics</strong> - Track your success metrics and growth</li>
                    <li>🤝 <strong>Supplier Network</strong> - Access to our verified supplier partners</li>
                </ul>
                ` : `
                <ul>
                    <li>🏢 <strong>Offer Management</strong> - Publish and manage your energy offers</li>
                    <li>📋 <strong>Tariff Grid Upload</strong> - Easy CSV import for your pricing structures</li>
                    <li>🔗 <strong>API Integration</strong> - Connect your systems for real-time pricing</li>
                    <li>📊 <strong>Market Analytics</strong> - Insights into market trends and competition</li>
                    <li>🎯 <strong>Lead Generation</strong> - Access to qualified energy customers</li>
                </ul>
                `}
            </div>
            
            <div style="text-align: center;">
                <a href="${registrationUrl}" class="cta-button">Complete Your Registration</a>
            </div>
            
            <div class="expiry">
                <strong>⏰ Important:</strong> This invitation expires in 7 days. Please complete your registration before the link expires.
            </div>
            
            <div class="details">
                <h3>What happens next?</h3>
                <ol>
                    <li><strong>Click the registration button</strong> above to access the secure registration form</li>
                    <li><strong>Complete your profile</strong> with your professional information</li>
                    <li><strong>Wait for approval</strong> - Our admin team will review and approve your account</li>
                    <li><strong>Start using the platform</strong> once your account is activated</li>
                </ol>
            </div>
            
            <div class="message">
                <p>If you have any questions about this invitation or need assistance with the registration process, please don't hesitate to contact our support team.</p>
                
                <p>We look forward to welcoming you to our platform!</p>
                
                <p>Best regards,<br>
                <strong>The ${this.companyName} Team</strong></p>
            </div>
        </div>
        
        <div class="footer">
            <p>This invitation was sent to ${inviteeDetails.email || email}. If you received this email in error, please ignore it.</p>
            <p>&copy; ${new Date().getFullYear()} ${this.companyName}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`;

    const textBody = `
Hello ${name},

You have been invited to join ${this.companyName} as a ${userTypeDisplay}!

Our platform connects energy professionals with clients seeking the best energy solutions.

To complete your registration, please visit:
${registrationUrl}

This invitation expires in 7 days.

What happens next?
1. Click the registration link above
2. Complete your profile with your professional information  
3. Wait for approval from our admin team
4. Start using the platform once activated

If you have any questions, please contact our support team.

Best regards,
The ${this.companyName} Team

---
This invitation was sent to ${inviteeDetails.email || email}.
© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.
`;

    return {
      subject,
      htmlBody,
      textBody
    };
  }

  getReminderEmailTemplate(userType, inviteeDetails, registrationUrl, reminderNumber) {
    const name = inviteeDetails.firstName ? 
      `${inviteeDetails.firstName} ${inviteeDetails.lastName || ''}`.trim() : 
      'there';
    
    const userTypeDisplay = userType === 'Broker' ? 'Energy Broker' : 'Energy Supplier';
    
    const subject = `Reminder: Complete Your ${this.companyName} Registration`;
    
    const htmlBody = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${subject}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 10px; overflow: hidden; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #f39c12 0%, #e74c3c 100%); color: white; padding: 30px; text-align: center; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 40px 30px; }
        .greeting { font-size: 18px; margin-bottom: 20px; color: #2c3e50; }
        .message { margin-bottom: 30px; font-size: 16px; line-height: 1.8; }
        .cta-button { display: inline-block; background: linear-gradient(135deg, #f39c12 0%, #e74c3c 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 50px; font-weight: 600; font-size: 16px; margin: 20px 0; transition: transform 0.2s; }
        .cta-button:hover { transform: translateY(-2px); }
        .footer { background-color: #f8f9fa; padding: 20px 30px; text-align: center; font-size: 14px; color: #6c757d; }
        .urgent { background-color: #fff3cd; border-left: 4px solid #f39c12; padding: 15px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⏰ Registration Reminder</h1>
            <p>${this.companyName}</p>
        </div>
        
        <div class="content">
            <div class="greeting">Hello ${name},</div>
            
            <div class="message">
                <p>This is a friendly reminder that you have a pending invitation to join <strong>${this.companyName}</strong> as a <strong>${userTypeDisplay}</strong>.</p>
                
                <p>We noticed you haven't completed your registration yet, and we don't want you to miss this opportunity!</p>
            </div>
            
            <div class="urgent">
                <strong>⚠️ Your invitation will expire soon!</strong><br>
                Please complete your registration as soon as possible to secure your access to our platform.
            </div>
            
            <div style="text-align: center;">
                <a href="${registrationUrl}" class="cta-button">Complete Registration Now</a>
            </div>
            
            <div class="message">
                <p>If you're experiencing any issues with the registration process or have questions about the platform, please don't hesitate to reach out to our support team.</p>
                
                <p>We're excited to have you join our growing network of energy professionals!</p>
                
                <p>Best regards,<br>
                <strong>The ${this.companyName} Team</strong></p>
            </div>
        </div>
        
        <div class="footer">
            <p>This is reminder #${reminderNumber + 1} for the invitation sent to ${inviteeDetails.email || email}.</p>
            <p>&copy; ${new Date().getFullYear()} ${this.companyName}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`;

    const textBody = `
Hello ${name},

This is a friendly reminder that you have a pending invitation to join ${this.companyName} as a ${userTypeDisplay}.

Your invitation will expire soon! Please complete your registration as soon as possible.

Registration link: ${registrationUrl}

If you have any questions, please contact our support team.

Best regards,
The ${this.companyName} Team

---
This is reminder #${reminderNumber + 1} for ${inviteeDetails.email || email}.
© ${new Date().getFullYear()} ${this.companyName}. All rights reserved.
`;

    return {
      subject,
      htmlBody,
      textBody
    };
  }

  async verifyEmailAddress(email) {
    try {
      const params = {
        EmailAddress: email
      };
      
      await ses.verifyEmailIdentity(params).promise();
      logger.info(`Email verification initiated for ${email}`);
      return true;
    } catch (error) {
      logger.error(`Failed to verify email ${email}:`, error);
      return false;
    }
  }

  async getSendingQuota() {
    try {
      const quota = await ses.getSendQuota().promise();
      return quota;
    } catch (error) {
      logger.error('Failed to get SES sending quota:', error);
      throw error;
    }
  }
}

module.exports = new EmailService();
