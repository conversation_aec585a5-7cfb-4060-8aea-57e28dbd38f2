.dashboard-layout {
  display: flex;
  min-height: 100vh;
  background-color: #ffffff;
  position: relative;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

.main-content {
  flex: 1;
  transition: margin-left 0.3s ease, width 0.3s ease;
  width: 100%;
  max-width: 100%;
  min-height: 100vh;
  overflow-x: hidden;
  box-sizing: border-box;
}

.sidebar-open .main-content {
  margin-left: 250px;
  width: calc(100% - 250px);
}

.sidebar-closed .main-content {
  margin-left: 60px;
  width: calc(100% - 60px);
}

.content-wrapper {
  padding: 15px 0;
  min-height: calc(100vh - 60px); /* Adjust based on header height */
  background-color: #fff;
  width: 100%;
  max-width: 100%;
}

.sidebar-open .content-wrapper {
  max-width: 100%;
  width: 100%;
  padding-left: 20px;
  padding-right: 20px;
}

.sidebar-closed .content-wrapper {
  max-width: 100%;
  width: 100%;
  padding-left: 20px;
  padding-right: 20px;
}

/* Sidebar toggle button styles moved to sidebar.css */

/* Responsive styles */
@media (min-width: 992px) {
  .dashboard-header {
    position: relative;
    z-index: 99997;
  }
}

/* For mobile and tablet */
@media (max-width: 991px) {
  .main-content {
    margin-left: 0 !important;
    width: 100% !important;
  }

  .content-wrapper {
    padding: 15px 15px;
    max-width: 100% !important;
    width: 100% !important;
  }

  .dashboard-layout {
    overflow-x: hidden; /* Prevent horizontal scrolling */
  }

  /* Force mobile layout for dashboard stats */
  .dashboard-stats-container {
    padding: 0;
    margin: 0 0 20px 0;
    width: 100%;
    box-sizing: border-box;
  }

  .dashboard-stats-container .dashboard-stats-grid {
    padding: 0;
    margin: 0;
    width: 100%;
    box-sizing: border-box;
  }
}

@media (max-width: 768px) {
  .content-wrapper {
    padding: 12px 10px;
  }

  /* Ensure the dashboard content is properly contained */
  .dashboard-content {
    width: 100%;
    padding: 0;
    overflow-x: hidden;
    box-sizing: border-box;
  }
}

@media (max-width: 480px) {
  .content-wrapper {
    padding: 8px 5px;
  }

  .dashboard-header {
    height: 60px;
    padding: 0 8px;
    position: relative;
    display: flex;
    align-items: center;
  }

  /* Improve mobile layout */
  .dashboard-content {
    padding: 0;
    margin: 0;
    width: 100%;
    box-sizing: border-box;
  }

  /* Ensure tables don't overflow */
  .table-container {
    max-width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}

@media (max-width: 320px) {
  .content-wrapper {
    padding: 8px;
  }
}

/* ========== COMPREHENSIVE MOBILE RESPONSIVENESS FIXES FOR DASHBOARD ========== */

/* Global mobile fixes for dashboard */
@media screen and (max-width: 768px) {
  /* Ensure all dashboard containers respect viewport width */
  .dashboard-layout,
  .main-content,
  .content-wrapper,
  .dashboard-content,
  .dashboard-header {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  /* Force mobile layout */
  .sidebar-open .main-content,
  .sidebar-closed .main-content {
    margin-left: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  .sidebar-open .content-wrapper,
  .sidebar-closed .content-wrapper {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  /* Fix dashboard stats mobile responsiveness */
  .dashboard-stats-container,
  .dashboard-stats-grid {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    padding: 0 !important;
    margin: 0 0 1rem 0 !important;
  }

  /* Fix dashboard cards mobile responsiveness */
  .dashboard-card,
  .stats-card {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 0 1rem 0 !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
  }

  /* Fix table mobile responsiveness */
  .table-container,
  .data-table {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }

  /* Fix form mobile responsiveness */
  .form-container,
  .form-group,
  .form-input {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
  }

  /* Fix button mobile responsiveness */
  .btn,
  .dashboard-btn {
    width: 100% !important;
    max-width: 300px !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
    margin: 0.5rem auto !important;
    display: block !important;
  }

  /* Fix modal mobile responsiveness */
  .modal,
  .modal-content {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    margin: 0 !important;
    padding: 1rem !important;
    box-sizing: border-box !important;
  }

  /* Fix any remaining overflow issues */
  .dashboard-layout * {
    max-width: 100% !important;
    box-sizing: border-box !important;
  }

  /* Fix specific dashboard elements */
  .dashboard-overview,
  .dashboard-section {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    padding: 1rem !important;
    margin: 0 !important;
  }

  /* Fix grid layouts */
  .dashboard-grid,
  .stats-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
  }
}
